# Woodpecker CI - 简洁测试流程
when:
  - branch: main
    event: [push, pull_request]


volumes:
  docker_cache:
    driver: local

steps:
  # 单元测试
  - name: unit-test
    image: golang:1.23.0
    environment:
      CGO_ENABLED: 0
    volumes:
      - go-mod-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
    commands:
      - echo "=== Unit Test Environment ==="
      - echo "Go version $(go version)"
      - echo "CGO_ENABLED $CGO_ENABLED"
      - go mod tidy
      - go test -v ./tests/unit/*.go

  # 集成测试 - 使用优化的 DinD 文件访问策略
  - name: integration-test
    image: coinflow/dind-go
    network_mode: host
    privileged: true
    environment:
      DOCKER_HOST: unix:///var/run/docker.sock
      CI: true
      SHARED_WORKSPACE: /tmp/shared-workspace
      CI_WORKSPACE: /workspace
      CGO_ENABLED: 0
    volumes:
      - go-mod-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
      - /var/run/docker.sock:/var/run/docker.sock
      # 优化策略：直接映射工作目录到宿主机共享路径
      - /tmp/shared-workspace:/workspace
    working_dir: /workspace
    commands:
      - |
        # 设置清理陷阱
        trap 'echo "Cleaning up Docker services..."; make docker-down || true' EXIT

        # 准备测试环境
        echo "=== Preparing test environment ==="
        echo "Go version $(go version)"
        echo "CGO_ENABLED $CGO_ENABLED"
        echo "SHARED_WORKSPACE: $SHARED_WORKSPACE"

        # 在共享工作空间创建必要的目录结构
        echo "=== Creating directories in shared workspace ==="
        mkdir -p ops/ltcdata/node1/db ops/ltcdata/node2/db
        mkdir -p ops/dogedata/node1/db ops/dogedata/node2/db
        mkdir -p ops/persistence/schema
        
        # 设置权限
        chmod -R 755 ops/ltcdata/ ops/dogedata/ ops/persistence/ || true

        # 清理并重新下载依赖
        go mod tidy
        go mod download

        # 启动 Docker Compose 服务
        echo "=== Starting Docker Compose services ==="
        cd ops && ./scripts/setup-docker.sh && docker-compose up  && cd ..

        # 等待服务启动
        echo "Waiting for services to be ready..."
        sleep 10

        # 运行集成测试
        echo "=== Running integration tests ==="
        go test -v ./tests/integrat/*.go

    depends_on:
      - unit-test
 
