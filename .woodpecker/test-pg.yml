# Woodpecker CI - 简洁测试流程
when:
  - branch: main
    event: [push, pull_request]


volumes:
  docker_cache:
    driver: local

steps:
  
  # 集成测试 - 使用优化的 DinD 文件访问策略
  - name: integration-test
    image: coinflow/dind-go
    network_mode: host
    privileged: true
    environment:
      DOCKER_HOST: unix:///var/run/docker.sock
      CI: true
      SHARED_WORKSPACE: /tmp/shared-workspace
      CI_WORKSPACE: /workspace
      CGO_ENABLED: 0
    volumes:
      - go-mod-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
      - /var/run/docker.sock:/var/run/docker.sock
      # 优化策略：直接映射工作目录到宿主机共享路径
      - /tmp/shared-workspace:/workspace
    working_dir: /workspace
    commands:
      - |
       
        
        

       
        # 启动 Docker Compose 服务
        echo "=== Starting Docker Compose services ==="
        cd ops && ./scripts/setup-docker.sh && docker-compose -f docker-compose-pg.yml up  && cd ..

       
    depends_on:
      - unit-test
 
