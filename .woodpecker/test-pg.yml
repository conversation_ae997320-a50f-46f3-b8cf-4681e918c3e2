# Woodpecker CI - 简洁测试流程
when:
  - branch: main
    event: [push, pull_request]


volumes:
  docker_cache:
    driver: local

steps:
  
  # 集成测试 - 使用优化的 DinD 文件访问策略
  - name: integration-test
    image: coinflow/dind-go
    network_mode: host
    privileged: true
    environment:
      DOCKER_HOST: unix:///var/run/docker.sock
      CI: true
      SHARED_WORKSPACE: /tmp/shared-workspace
      CI_WORKSPACE: /workspace
      CGO_ENABLED: 0
    volumes:
      - go-mod-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
      - /var/run/docker.sock:/var/run/docker.sock
      # 优化策略：直接映射工作目录到宿主机共享路径
      - /tmp/shared-workspace:/workspace
    working_dir: /workspace
    commands:
      - |
        # 设置清理陷阱
        trap 'echo "Cleaning up Docker services..."; cd ops && docker-compose -f docker-compose-pg.yml down -v || true' EXIT

        # 准备测试环境
        echo "=== Preparing test environment ==="
        echo "Go version $(go version)"
        echo "CGO_ENABLED $CGO_ENABLED"

        # 启动 Docker Compose 服务
        echo "=== Starting PostgreSQL service ==="
        cd ops && ./scripts/setup-docker.sh && docker-compose -f docker-compose-pg.yml up -d  && cd ..

        # 等待并测试 PostgreSQL 端口连接
        echo "=== Testing PostgreSQL port connectivity ==="

        # 检查容器状态
        echo "Checking PostgreSQL container status..."
        cd ops && docker-compose -f docker-compose-pg.yml ps && cd ..

        # 测试端口连接 - 5次尝试，每次间隔5秒
        i=1
        while [ $i -le 5 ]; do
          echo "Port connectivity test $i/5..."

          # 尝试通过容器名称访问（DinD 环境中更可靠）
          if nc -z postgres 5432 > /dev/null 2>&1; then
            echo "✅ PostgreSQL port 5432 is accessible via container name on attempt $i"
            echo "=== PostgreSQL service is ready ==="
            exit 0
          # 备用：尝试通过 localhost 访问
          elif nc -z localhost 5432 > /dev/null 2>&1; then
            echo "✅ PostgreSQL port 5432 is accessible via localhost on attempt $i"
            echo "=== PostgreSQL service is ready ==="
            exit 0
          else
            echo "❌ Port connectivity test $i failed (tried both postgres and localhost)"
            if [ $i -eq 5 ]; then
              echo "💥 All 5 port connectivity tests failed. PostgreSQL service is not ready."
              echo "Final container status check:"
              cd ops && docker-compose -f docker-compose-pg.yml ps && cd ..
              exit 1
            fi
            echo "Waiting 5 seconds before next attempt..."
            sleep 5
          fi
          i=$((i + 1))
        done