# Woodpecker CI - 简洁测试流程
when:
  - branch: main
    event: [push, pull_request]


volumes:
  docker_cache:
    driver: local

steps:
  
  # 集成测试 - 使用优化的 DinD 文件访问策略
  - name: integration-test
    image: coinflow/dind-go
    network_mode: host
    privileged: true
    environment:
      DOCKER_HOST: unix:///var/run/docker.sock
      CI: true
      SHARED_WORKSPACE: /tmp/shared-workspace
      CI_WORKSPACE: /workspace
      CGO_ENABLED: 0
    volumes:
      - go-mod-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
      - /var/run/docker.sock:/var/run/docker.sock
      # 优化策略：直接映射工作目录到宿主机共享路径
      - /tmp/shared-workspace:/workspace
    working_dir: /workspace
    commands:
      - |
        # 设置清理陷阱
        trap 'echo "Cleaning up Docker services..."; cd ops && docker-compose -f docker-compose-pg.yml down -v || true' EXIT

        # 准备测试环境
        echo "=== Preparing test environment ==="
        echo "Go version $(go version)"
        echo "CGO_ENABLED $CGO_ENABLED"

        # 启动 Docker Compose 服务
        echo "=== Starting PostgreSQL service ==="
        cd ops && ./scripts/setup-docker.sh && docker-compose -f docker-compose-pg.yml up -d && cd ..

        # 等待并测试 PostgreSQL 连接
        echo "=== Testing PostgreSQL connection ==="

        # 安装 PostgreSQL 客户端
        apt-get update && apt-get install -y postgresql-client

        # 测试数据库连接 - 5次尝试，每次间隔5秒
        for i in {1..5}; do
          echo "Connection attempt $i/5..."
          if PGPASSWORD=password psql -h localhost -U root -d pool -c "SELECT 1;" > /dev/null 2>&1; then
            echo "✅ PostgreSQL connection successful on attempt $i"
            echo "=== PostgreSQL is ready for use ==="
            exit 0
          else
            echo "❌ Connection attempt $i failed"
            if [ $i -eq 5 ]; then
              echo "💥 All 5 connection attempts failed. PostgreSQL is not ready."
              exit 1
            fi
            echo "Waiting 5 seconds before next attempt..."
            sleep 5
          fi
        done